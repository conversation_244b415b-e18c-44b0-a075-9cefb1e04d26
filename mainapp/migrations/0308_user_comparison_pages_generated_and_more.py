# Generated by Django 5.0 on 2025-08-01 06:29

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("mainapp", "0307_rename_next_billing_date_user_next_renewal_date"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="comparison_pages_generated",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="user",
            name="last_comparison_pages_reset",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name="user",
            name="last_stats_pages_reset",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name="user",
            name="stats_pages_generated",
            field=models.IntegerField(default=0),
        ),
        migrations.CreateModel(
            name="AIComparisonPage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("comp_id", models.CharField(max_length=100, unique=True)),
                ("comparison_type", models.CharField(max_length=100)),
                ("url1", models.URLField(max_length=500)),
                ("url2", models.URLField(max_length=500)),
                ("conversation", models.JSONField(blank=True, default=list)),
                ("created_on", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_on", models.DateTimeField(auto_now=True)),
                ("current_version_id", models.IntegerField(blank=True, null=True)),
                (
                    "verification_token",
                    models.CharField(editable=False, max_length=150, unique=True),
                ),
                ("is_verified", models.BooleanField(default=False)),
                (
                    "website",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="mainapp.website",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_on"],
            },
        ),
        migrations.CreateModel(
            name="AIShareWidget",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "widget_id",
                    models.CharField(db_index=True, max_length=300, unique=True),
                ),
                ("name", models.CharField(max_length=250)),
                ("prompt_template", models.TextField()),
                (
                    "text_before_button",
                    models.CharField(
                        blank=True,
                        default="Summarize & Talk with this Page on :",
                        max_length=500,
                    ),
                ),
                ("selected_llms", models.JSONField(default=list)),
                (
                    "style",
                    models.CharField(
                        choices=[
                            ("Horizontal with Logos", "Horizontal with Logos"),
                            ("Stacked with Logos", "Stacked with Logos"),
                            ("Horizontal with Icon", "Horizontal with Icon"),
                            ("Stacked with Icon", "Stacked with Icon"),
                            ("Horizontal with Buttons", "Horizontal with Buttons"),
                        ],
                        default="Horizontal with Logos",
                        max_length=50,
                    ),
                ),
                ("html_code", models.TextField(blank=True, default="")),
                ("html_code_dark", models.TextField(blank=True, default="")),
                ("is_processing", models.BooleanField(default=True)),
                ("is_active", models.BooleanField(default=True)),
                ("total_clicks", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "current_plan_name",
                    models.CharField(blank=True, default="free", max_length=50),
                ),
                (
                    "website",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="mainapp.website",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="AIShareWidgetClick",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("llm", models.CharField(max_length=50)),
                ("destination_url", models.URLField()),
                ("clicked_at", models.DateTimeField(auto_now_add=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True, null=True)),
                (
                    "widget",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="clicks",
                        to="mainapp.aisharewidget",
                    ),
                ),
            ],
            options={
                "ordering": ["-clicked_at"],
            },
        ),
        migrations.CreateModel(
            name="AIStatsPage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("stats_id", models.CharField(max_length=100, unique=True)),
                ("stats_type", models.CharField(max_length=100)),
                ("stats_topic", models.CharField(max_length=500)),
                ("stats_description", models.TextField(blank=True, null=True)),
                ("conversation", models.JSONField(blank=True, default=list)),
                (
                    "original_keyword",
                    models.CharField(blank=True, max_length=200, null=True),
                ),
                ("selected_idea", models.JSONField(blank=True, default=dict)),
                ("created_on", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_on", models.DateTimeField(auto_now=True)),
                ("current_version_id", models.IntegerField(blank=True, null=True)),
                (
                    "verification_token",
                    models.CharField(editable=False, max_length=150, unique=True),
                ),
                ("is_verified", models.BooleanField(default=False)),
                (
                    "website",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="mainapp.website",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_on"],
            },
        ),
        migrations.CreateModel(
            name="AIComparisonPageVersion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("version_name", models.CharField(max_length=150)),
                ("html_code", models.TextField()),
                ("changes_summary", models.TextField(blank=True, null=True)),
                ("created_on", models.DateTimeField(default=django.utils.timezone.now)),
                (
                    "is_current",
                    models.BooleanField(
                        default=False,
                        help_text="Indicates if this is the current active version",
                    ),
                ),
                (
                    "comparison_page",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="mainapp.aicomparisonpage",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_on"],
                "unique_together": {("comparison_page", "version_name")},
            },
        ),
        migrations.CreateModel(
            name="AIStatsPageVersion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("version_name", models.CharField(max_length=150)),
                ("html_code", models.TextField()),
                ("changes_summary", models.TextField(blank=True, null=True)),
                ("created_on", models.DateTimeField(default=django.utils.timezone.now)),
                (
                    "is_current",
                    models.BooleanField(
                        default=False,
                        help_text="Indicates if this is the current active version",
                    ),
                ),
                (
                    "stats_page",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="mainapp.aistatspage",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_on"],
                "unique_together": {("stats_page", "version_name")},
            },
        ),
    ]
