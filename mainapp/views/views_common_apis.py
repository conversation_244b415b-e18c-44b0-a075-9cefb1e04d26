import os
import json
import base64
import hashlib
import logging
import re
import time
from typing import Dict

from django.core.handlers.wsgi import WSGIRequest
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.clickjacking import xframe_options_exempt
from django.http import JsonResponse, HttpResponse, StreamingHttpResponse

import openai

import tldextract
import cryptography.fernet
from rest_framework.request import Request
from rest_framework.decorators import api_view
from cryptography.fernet import Fernet, InvalidToken

from AbunDRFBackend import settings
from mainapp.utils import decrypt_dict
from mainapp.serializers import ChangeLogSerializer
from mainapp.json_responses import JsonResponseBadRequest
from mainapp.models import ChangeLog, AICalculator, AIStreamingToken, User, Website, WebPage, AIStatsPage, AIComparisonPage, AIShareWidget


logger = logging.getLogger(__name__)

@api_view(['GET'])
def get_changelogs(request: Request):
    """
    Get Changelogs.

    :param request: Django Rest Framework's Request object.
    """
    return JsonResponse(
        status=200,
        data=ChangeLogSerializer(ChangeLog.objects.all().order_by("-created_at"), many=True).data,
        safe=False,
    )


@api_view(['OPTIONS', 'GET'])
def get_calculator_embed_script(request: Request):
    """
    API view to generate a customized JavaScript embed script for a specific calculator.
    :param request: Django Rest Framework's Request object.
    """
    encrypted_data = request.query_params.get('data-calc', None)

    # Validate the request has required parameters
    if not encrypted_data:
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_PARAMETERS"})

    try:
        script_data = decrypt_dict(encrypted_data)
        calculator_id = script_data['CALCULATOR_ID']
        button_color = script_data.get('BUTTON_COLOR', '#007bff')
        website_name = script_data.get('WEBSITE_NAME', 'Website')
        calculator_title = script_data.get('CALCULATOR_TITLE', 'Calculator')
    except (KeyError, ValueError, cryptography.fernet.InvalidToken):
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_DATA"})

    try:
        AICalculator.objects.get(calculator_id=calculator_id)
    except AICalculator.DoesNotExist:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_CALCULATOR_FOUND"})

    # Read the template script file
    script_path = os.path.join(settings.BASE_DIR, 'tools_scripts', 'ai_calculator', 'calculator_embed.js')
    with open(script_path, 'r') as file:
        script_content = file.read()

    # Replace placeholders
    server_url = request.build_absolute_uri('/')[:-1].replace('http://', 'https://')
    script_content = script_content.replace('CALCULATOR_ID', calculator_id)
    script_content = script_content.replace('SERVER_URL', server_url)
    script_content = script_content.replace('BUTTON_COLOR', button_color)
    script_content = script_content.replace('WEBSITE_NAME', website_name)
    script_content = script_content.replace('CALCULATOR_TITLE', calculator_title)

    # Set up the response with the proper CORS headers - allow all origins
    response = HttpResponse(script_content, content_type='application/javascript')
    response['Access-Control-Allow-Origin'] = '*'
    response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
    response['Access-Control-Allow-Headers'] = 'Content-Type'
    response['Cache-Control'] = 'max-age=3600'

    return response


@api_view(['OPTIONS', 'GET'])
@xframe_options_exempt
def get_calculator_code(request: Request, calculator_id: str):
    """
    API view to get the calculator code
    :param request: Django Rest Framework's Request object.
    :param calculator_id: The unique ID of the calculator to retrieve
    """
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = HttpResponse()
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type'
        return response

    # Validate calculator_id format
    if not calculator_id or not isinstance(calculator_id, str) or len(calculator_id) > 100:
        logger.error(f"Invalid calculator ID format: {calculator_id}")
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_CALCULATOR_ID"})

    try:
        ai_calculator: AICalculator = AICalculator.objects.get(calculator_id=calculator_id)
    except AICalculator.DoesNotExist:
        logger.error(f"No calculator found with {calculator_id} calculator ID.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_CALCULATOR_FOUND"})

    # Get the html code
    html_code: str = ai_calculator.html_code

    # Create response with CORS headers
    response = HttpResponse(html_code, content_type="text/html")
    response['Access-Control-Allow-Origin'] = '*'
    response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
    response['Cache-Control'] = 'max-age=3600'  # Cache for 1 hour

    return response


@csrf_exempt
def stream_ai_response(request: WSGIRequest, token: str):
    """
    Streams the AI response
    :param request: Django Rest Framework's Request object
    :param token: Valid AI streaming token
    """
    if request.method == "POST":
        try:
            ai_streaming_token = AIStreamingToken.objects.get(token=token)
        except AIStreamingToken.DoesNotExist:
            return JsonResponseBadRequest(additional_data={"err_id": 'INVALID_TOKEN', "message": "Invalid token"})

        if not ai_streaming_token.is_valid():
            return JsonResponseBadRequest(additional_data={"err_id": 'EXPIRED_TOKEN', "message": "Token expired or invalid"})

        # get the payload from the body
        payload: Dict = json.loads(request.body.decode())

        # update the prompt if its a translation request
        translation_matching_prompt = """
        请帮我翻译以上内容，在翻译之前，想先判断一下这个内容是不是中文，如果是中文，则翻译问英文，如果是其他语言，则需要翻译为中文，注意，你只需要返回翻译的结果，不需要对此进行任何解释，不需要除了翻译结果以外的其他任何内容
        """.replace("\n", "").strip()

        updated_prompt = """
        Please help me translate the above content. Before translating, I want to determine whether the content is in English (US). If it is in English (US), translate it into English (UK). If it is in other languages, translate it into English (US). Please note that you only need to return the translation result, and you do not need to explain it or any other content except the translation result.
        """.replace("\n", "").strip()

        if translation_matching_prompt in payload['messages'][0]['content']:
            payload['messages'][0]['content'] = payload['messages'][0]['content'].replace(translation_matching_prompt, updated_prompt)

        def generate():
            client = openai.Client()
            try:
                # Create the streaming completion with the new API
                stream = client.chat.completions.create(**payload)
            except openai._exceptions.InternalServerError:
                return JsonResponseBadRequest(additional_data={"err_id": 'SERVER_ERROR', "message": "Server error"})

            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield f"data: {json.dumps({'choices': [{'delta': {'content': chunk.choices[0].delta.content}}]})}\n\n"

        # mark the token as used
        ai_streaming_token.used = True
        ai_streaming_token.save()

        return StreamingHttpResponse(generate(), content_type="text/event-stream")

    return JsonResponseBadRequest(additional_data={"err_id": 'METHOD_NOT_ALLOWED', "message": "Requested method is not allowed"})


@api_view(['OPTIONS', 'GET'])
def get_tools_loading_script(request: Request):
    """
    API view to generate a customized JavaScript for loading all tools.
    :param request: Django Rest Framework's Request object.
    """
    try:
        # Read the template script file
        script_path = os.path.join(settings.BASE_DIR, 'tools_scripts', 'get_all_tools_loading_script.js')
        
        # Verify file exists
        if not os.path.exists(script_path):
            logger.error(f"Script file not found at {script_path}")
            # Return JavaScript error rather than HTML
            return HttpResponse(
                "console.error('Script file not found on server');", 
                content_type='application/javascript'
            )
            
        with open(script_path, 'r') as file:
            script_content = file.read()

        # Replace placeholders securely
        server_url: str = request.build_absolute_uri('/')[:-1].replace('http://', 'https://')
        script_content = script_content.replace('SERVER_URL', f"{server_url}/api/frontend/load-tools-scripts/")

        # Set content type and additional headers
        response = HttpResponse(script_content, content_type='application/javascript')
        
        # Add cache-control and CORS headers
        response['Cache-Control'] = 'max-age=3600'
        
        # Set CORS headers to allow any domain to load this script
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
        
        return response
        
    except Exception as e:
        # Log the error but return a JavaScript response
        logger.critical(f"Error serving script: {str(e)}")
        return HttpResponse(
            f"console.error('Error loading calculator script: {str(e)}');", 
            content_type='application/javascript'
        )


@api_view(['OPTIONS', 'GET'])
def load_tools_scripts(request: Request):
    """
    API view to generate a customized JavaScript for loading all tools.
    :param request: Django Rest Framework's Request object.
    """
    try:
        encrpted_user_email: str = request.query_params["user-id"]
        page_url: str = request.query_params["url"]
    except KeyError:
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_PARAMETERS"})

    # Decrypt the user email
    key = base64.urlsafe_b64encode(hashlib.sha256(settings.SECRET_KEY.encode()).digest())
    f = Fernet(key)

    # Decrypt the user email
    try:
        user_email = f.decrypt(encrpted_user_email.encode()).decode()
    except InvalidToken:
        logger.error(f"Invalid user email token: {user_email}")
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_USER_EMAIL"})
    except Exception as e:
        logger.critical(f"Error decrypting user email: {str(e)}")
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_USER_EMAIL"})

    # Get the user
    try:
        user: User = User.objects.get(email=user_email)
    except User.DoesNotExist:
        logger.error(f"No user found with {user_email} email.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_USER_FOUND"})

    # Extract the domain from the page url
    domain_extract: str = tldextract.extract(page_url)

    if domain_extract.subdomain:
        domain: str = f"{domain_extract.subdomain}.{domain_extract.domain}.{domain_extract.suffix}"
    else:
        domain: str = f"{domain_extract.registered_domain}"

    # Get the website
    try:
        website: Website = user.website_set.get(domain=domain)
    except Website.DoesNotExist:
        logger.error(f"No website found with {domain} domain.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_FOUND"})

    # List to store all script contents
    script_contents = []

    if website.auto_schema_enabled:
        # Auto Schema Tool - load if website has webpages
        # Get the webpage
        try:
            webpage: WebPage = website.webpage_set.get(url=page_url)
        except WebPage.DoesNotExist:
            logger.error(f"No webpage found with {page_url} url.")
            webpage = None

        if webpage and webpage.schema_enabled:
            try:
                auto_schema_script_path = os.path.join(settings.BASE_DIR, 'tools_scripts', 'auto_schema', 'auto_schema_tool.js')

                if os.path.exists(auto_schema_script_path):
                    with open(auto_schema_script_path, 'r') as file:
                        auto_schema_content = file.read()

                        # Convert schema to string
                        schema_string = json.dumps(webpage.schema)

                        # Replace placeholders
                        auto_schema_content = auto_schema_content.replace("'JSONLD_SCHEMA'", schema_string)
                        script_contents.append(auto_schema_content)

                else:
                    logger.error(f"Auto schema script not found at {auto_schema_script_path}")

            except Exception as e:
                logger.critical(f"Error loading auto schema script: {str(e)}")
    
    # Future tools can be added here with similar pattern:
    # Example:
    # if website.some_other_tool_enabled:
    #     try:
    #         other_tool_script_path = os.path.join(settings.BASE_DIR, 'tools_scripts', 'other_tool', 'other_tool.js')
    #         if os.path.exists(other_tool_script_path):
    #             with open(other_tool_script_path, 'r') as file:
    #                 other_tool_content = file.read()
    #                 other_tool_content = other_tool_content.replace('SERVER_URL', server_url)
    #                 script_contents.append(other_tool_content)
    #     except Exception as e:
    #         logger.error(f"Error loading other tool script: {str(e)}")
    
    # Combine all scripts
    if script_contents:
        combined_script = '\n\n'.join(script_contents)

    else:
        # Return empty script if no tools are active
        combined_script = '// No tools are currently active for this website'
    
    # Set up the response with proper headers
    response = HttpResponse(combined_script, content_type='application/javascript')
    response['Cache-Control'] = 'max-age=3600'
    response['Access-Control-Allow-Origin'] = '*'
    response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
    
    return response


@api_view(['GET'])
@csrf_exempt
def get_stats_jsonp(request: Request, stats_id: str):
    """
    Enhanced JSONP endpoint with better chart support
    """
    callback = request.GET.get('callback', 'callback')
    plan = request.GET.get('plan', '')
    
    # Sanitize callback name to prevent XSS
    if not re.match(r'^[a-zA-Z_$][a-zA-Z0-9_$]*$', callback):
        callback = 'callback'
    
    try:
        ai_stats_page = AIStatsPage.objects.get(stats_id=stats_id)
        current_version = ai_stats_page.get_current_version()
        
        if current_version:
            html_content = current_version.html_code
            
            # Extract original styles from HTML content
            style_start = html_content.find('<style>')
            style_end = html_content.find('</style>') + 8
            original_styles = ""
            if style_start != -1 and style_end != -1:
                original_styles = html_content[style_start:style_end]
            
            # Add trial banner for trial plan users
            trial_banner = ""
            if plan.lower() == 'trial':
                trial_banner = '''
                <div class="custom-banner">
                    <div class="container">
                        <div class='button'>
                            <img style="width: 18px; margin-right: 8px" src="https://abun.com/wp-content/uploads/2025/06/Ai-icon.svg">
                            Made with <a href="https://abun.com" target="_blank"><span style="text-decoration:underline; color:#000000; margin-left:5px;"> Abun.com</span></a>
                        </div>
                    </div>
                </div>
                <style>
                .custom-banner {
                    background-color: #f3f4f6;
                    padding: 20px 16px;
                    border-bottom: 1px solid #d1d5db;
                    font-size: 14px;
                    color: #374151;
                    text-align: center;
                    min-height: 60px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .container{
                    display:flex;
                }
                .button{
                    font-family: "Inter", Sans-serif;
                    font-size: 14px;
                    display: flex;
                    border-style: solid;
                    border-width: 1px 1px 1px 1px;
                    border-color: #D2D2EB;
                    border-radius: 200px;
                    padding: 15px;
                    transition: all .09s ease !important;
                    box-shadow: 0px 3px 3px 0px rgba(19, 48, 66, 0.07);
                }
                .button:hover{
                    transform: scale(1.02);
                    cursor: auto !important;
                    box-shadow: -2px 5px 3px 0px rgba(19, 48, 66, 0.07);
                }
                .button:hover span{
                    color:#2942ff!important;
                }
                </style>
                '''
            
            # Wrap content with minimal style isolation that preserves original styles
            isolated_content = f"""
            <div class="abun-stats-container" data-stats-id="{stats_id}">
                <style>
                /* Minimal isolation - only reset what's necessary */
                .abun-stats-container {{
                    display: block !important;
                    width: 100% !important;
                    min-height: 400px !important;
                    position: relative !important;
                    box-sizing: border-box !important;
                }}
                
                /* Preserve chart containers */
                .abun-stats-container .chart-container {{
                    position: relative !important;
                    width: 100% !important;
                    height: auto !important;
                }}
                
                .abun-stats-container canvas {{
                    max-width: 100% !important;
                    height: auto !important;
                    display: block !important;
                }}
                </style>
                {original_styles}
                {html_content}
                {trial_banner}
            </div>
            """
        else:
            isolated_content = '<div style="padding: 20px; color: #666; font-family: Arial, sans-serif;">No content available</div>'
        
        json_data = {
            'success': True,
            'html': isolated_content,
            'statsId': stats_id,
            'timestamp': int(time.time())
        }
        
        jsonp_response = f"{callback}({json.dumps(json_data, ensure_ascii=False)});"
        
    except Exception as e:
        logger.critical(f"JSONP endpoint error for stats_id {stats_id}: {str(e)}", exc_info=True)
        json_data = {
            'success': False,
            'error': str(e),
            'statsId': stats_id
        }
        jsonp_response = f"{callback}({json.dumps(json_data)});"
    
    response = HttpResponse(jsonp_response, content_type='application/javascript; charset=utf-8')
    response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response['Pragma'] = 'no-cache'
    response['Expires'] = '0'
    return response



@api_view(['GET'])
@csrf_exempt
def get_comparison_jsonp(request: Request, comp_id: str):
    """
    Enhanced JSONP endpoint with better chart support
    """
    callback = request.GET.get('callback', 'callback')
    plan = request.GET.get('plan', '')
    
    if not re.match(r'^[a-zA-Z_$][a-zA-Z0-9_$]*$', callback):
        callback = 'callback'
    
    try:
        ai_comparison_page = AIComparisonPage.objects.get(comp_id=comp_id)
        current_version = ai_comparison_page.get_current_version()
        
        if current_version:
            html_content = current_version.html_code
            
            style_start = html_content.find('<style>')
            style_end = html_content.find('</style>') + 8
            original_styles = ""
            if style_start != -1 and style_end != -1:
                original_styles = html_content[style_start:style_end]
            
            # Add trial banner if plan is 'trial'
            trial_banner = ""
            if plan.lower() == 'trial':
                trial_banner = '''
                <div class="custom-banner">
                    <div class="container">
                        <div class='button'>
                            <img style="width: 18px; margin-right: 8px" src="https://abun.com/wp-content/uploads/2025/06/Ai-icon.svg">
                            Made with <a href="https://abun.com" target="_blank"><span style="text-decoration:underline; color:#000000; margin-left:5px;"> Abun.com</span></a>
                        </div>
                    </div>
                </div>
                <style>
                .custom-banner {
                    background-color: #f3f4f6 !important;
                    padding: 20px 16px !important;
                    border-bottom: 1px solid #d1d5db !important;
                    font-size: 14px !important;
                    color: #374151 !important;
                    text-align: center !important;
                    min-height: 60px !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    width: 100% !important;
                    box-sizing: border-box !important;
                }
                .container{
                    display: flex !important;
                    justify-content: center !important;
                    align-items: center !important;
                }
                .button{
                    font-family: "Inter", Sans-serif !important;
                    font-size: 14px !important;
                    display: flex !important;
                    border-style: solid !important;
                    border-width: 1px 1px 1px 1px !important;
                    border-color: #D2D2EB !important;
                    border-radius: 200px !important;
                    padding: 15px !important;
                    transition: all .09s ease !important;
                    box-shadow: 0px 3px 3px 0px rgba(19, 48, 66, 0.07) !important;
                    align-items: center !important;
                    justify-content: center !important;
                    text-decoration: none !important;
                    background-color: white !important;
                }
                .button:hover{
                    transform: scale(1.02) !important;
                    cursor: auto !important;
                    box-shadow: -2px 5px 3px 0px rgba(19, 48, 66, 0.07) !important;
                }
                .button:hover span{
                    color: #2942ff !important;
                }
                .button img {
                    width: 18px !important;
                    margin-right: 8px !important;
                    display: inline-block !important;
                }
                .button a {
                    text-decoration: none !important;
                    color: inherit !important;
                }
                .button span {
                    text-decoration: underline !important;
                    color: #000000 !important;
                    margin-left: 5px !important;
                }
                </style>
                '''
            
            isolated_content = f"""
            <div class="abun-comp-container" data-comp-id="{comp_id}">
                <style>
                /* Minimal isolation - only reset what's necessary */
                .abun-comp-container {{
                    display: block !important;
                    width: 100% !important;
                    min-height: 400px !important;
                    position: relative !important;
                    box-sizing: border-box !important;
                }}
                
                /* Preserve chart containers */
                .abun-comp-container .chart-container {{
                    position: relative !important;
                    width: 100% !important;
                    height: auto !important;
                }}
                
                .abun-comp-container canvas {{
                    max-width: 100% !important;
                    height: auto !important;
                    display: block !important;
                }}
                </style>
                {original_styles}
                {html_content}
                {trial_banner}
            </div>
            """
        else:
            isolated_content = '<div style="padding: 20px; color: #666; font-family: Arial, sans-serif;">No content available</div>'
        
        json_data = {
            'success': True,
            'html': isolated_content,
            'compId': comp_id,
            'timestamp': int(time.time())
        }
        
        jsonp_response = f"{callback}({json.dumps(json_data, ensure_ascii=False)});"
        
    except AIComparisonPage.DoesNotExist:
        logger.error(f"Comparison page not found: {comp_id}")
        json_data = {
            'success': False,
            'error': 'Comparison page not found',
            'compId': comp_id
        }
        jsonp_response = f"{callback}({json.dumps(json_data)});"
    except Exception as e:
        logger.critical(f"Unexpected error in get_comparison_jsonp for comp_id {comp_id}: {str(e)}", exc_info=True)
        json_data = {
            'success': False,
            'error': 'Internal server error',
            'compId': comp_id
        }
        jsonp_response = f"{callback}({json.dumps(json_data)});"
    
    response = HttpResponse(jsonp_response, content_type='application/javascript; charset=utf-8')
    response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response['Pragma'] = 'no-cache'
    response['Expires'] = '0'
    return response



@api_view(['GET'])
@csrf_exempt
def get_share_widget_jsonp(request: Request, widget_id: str):
    """
    JSONP endpoint for AI Share Widgets with light/dark mode support
    """
    callback = request.GET.get('callback', 'callback')
    mode = request.GET.get('mode', 'light')
    
    # Sanitize callback name for security
    if not re.match(r'^[a-zA-Z_$][a-zA-Z0-9_$]*$', callback):
        callback = 'callback'
    
    try:
        ai_share_widget = AIShareWidget.objects.get(widget_id=widget_id, is_active=True)
        
        # Select appropriate HTML content based on mode
        if mode == 'dark' and hasattr(ai_share_widget, 'html_code_dark') and ai_share_widget.html_code_dark:
            html_content = ai_share_widget.html_code_dark
        else:
            html_content = ai_share_widget.html_code
        
        if html_content:
            # Wrap content with isolation container
            isolated_content = f"""
            <div class="abun-share-container abun-share-{mode}" data-widget-id="{widget_id}" data-mode="{mode}">
                <style>
                /* Container isolation styles */
                .abun-share-container {{
                    display: block !important;
                    width: 100% !important;
                    position: relative !important;
                    box-sizing: border-box !important;
                    margin: 0 !important;
                    padding: 0 !important;
                }}
                
                /* Mode-specific theming */
                .abun-share-light {{
                    background-color: #ffffff !important;
                    color: #000000 !important;
                }}
                
                .abun-share-dark {{
                    background-color: #1a1a1a !important;
                    color: #ffffff !important;
                }}
                </style>
                {html_content}
            </div>
            """
        else:
            isolated_content = f'<div style="padding: 20px; color: #666; font-family: Arial, sans-serif;">Widget content not available for {mode} mode</div>'
        
        json_data = {
            'success': True,
            'html': isolated_content,
            'widgetId': widget_id,
            'mode': mode,
            'timestamp': int(time.time())
        }
        
        jsonp_response = f"{callback}({json.dumps(json_data, ensure_ascii=False)});"
        
    except Exception as e:
        logger.critical(f"Failed to load share widget JSONP for widget_id={widget_id}, mode={mode}: {str(e)}", exc_info=True)
        
        json_data = {
            'success': False,
            'error': str(e),
            'widgetId': widget_id,
            'mode': mode
        }
        jsonp_response = f"{callback}({json.dumps(json_data)});"
    
    response = HttpResponse(jsonp_response, content_type='application/javascript; charset=utf-8')
    response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response['Pragma'] = 'no-cache'
    response['Expires'] = '0'
    return response

