import logging

import stripe
from django.utils import timezone
from django.core.management.base import BaseCommand

from mainapp.models import AppSumoLicense
from mainapp.utils import get_next_renewal_date
from mainapp.stripe_utils import get_stripe_product_data


logger = logging.getLogger(__name__)

class Command(BaseCommand):

    def handle(self, *args, **kwargs):
        current_time = timezone.now()
        logger.info(f"Starting usage counter reset at {current_time}")

        licenses = AppSumoLicense.objects.filter(
            license_status='active',
            next_renewal_date__lte=current_time
        )

        reset_count = 0

        for license in licenses:
            try:
                user = license.user

                if not user:
                    logger.error(
                        f"License key '{license.license_key}' is not associated with any user account.\n"
                        f"License status: {license.license_status},\n"
                        f"Created on: {license.created_on},\n"
                        f"Next renewal date: {license.next_renewal_date}\n\n"
                    )
                    continue

                # Get user's display plan name (e.g., LTD & Trial, LTD & Basic, LTD & Pro)
                try:
                    display_plan_name = get_stripe_product_data(user)['display_name']
                except stripe.error.InvalidRequestError:
                    display_plan_name = 'Trial'

                # Reset limits for users on Trial plan
                if 'Trial' in display_plan_name:
                    # Store old values for logging
                    old_values = {
                        'articles': user.articles_generated,
                        'keywords': user.keywords_generated,
                        'titles': user.titles_generated,
                        'email': user.blog_emails_found,
                        'glossary_topic': user.glossary_topic_generated,
                        'glossary_contents': user.glossary_contents_generated,
                        'guest_post_finder_queries': user.guest_post_finder_queries_generated,
                        'reddit_post_finder_queries': user.reddit_post_finder_queries_generated,
                        'ai_calculators': user.ai_calculators_generated,
                        'automation_projects': user.automation_projects_generated,
                        'old_renewal_date': license.next_renewal_date,
                        'fast_indexing': user.fast_indexing_generated,
                        'search_console_insights': user.search_console_insights_generated,
                        'content_calendar': user.content_calendar_generated,
                    }

                    # Reset counters
                    user.articles_generated = 0
                    user.keywords_generated = 0
                    user.titles_generated = 0
                    user.blog_emails_found = 0
                    user.ai_calculators_generated = 0
                    user.glossary_topic_generated = 0
                    user.glossary_contents_generated = 0
                    user.guest_post_finder_queries_generated = 0
                    user.reddit_post_finder_queries_generated = 0
                    user.content_calendar_generated = 0
                    user.search_console_insights_generated = 0
                    user.fast_indexing_generated = 0
                    user.stats_pages_generated = 0
                    user.comparison_pages_generated = 0

                    # Update next renewal date
                    license.next_renewal_date = get_next_renewal_date()

                    # Save changes
                    user.save()
                    license.save()

                    reset_count += 1

                    self.stdout.write(
                        f"Reset counters for '{user.email}' (License: {license.license_key}).\n"
                        f"Old values - Articles: {old_values['articles']},\n"
                        f"Keywords: {old_values['keywords']},\n"
                        f"Titles: {old_values['titles']}.\n"
                        f"Emails: {old_values['email']}.\n"
                        f"Glossary Topic: {old_values['glossary_topic']}.\n"
                        f"Glossary Contents: {old_values['glossary_contents']}.\n"
                        f"Guest Post Finder Queries: {old_values['guest_post_finder_queries']}.\n"
                        f"Reddit Post Finder Queries: {old_values['reddit_post_finder_queries']}.\n"
                        f"AI Calculators: {old_values['ai_calculators']}.\n"
                        f"Automation Projects: {old_values['automation_projects']}.\n"
                        f"Fast Indexing: {old_values['fast_indexing']}.\n"
                        f"Search Console Insights: {old_values['search_console_insights']}.\n"
                        f"Content Calendar: {old_values['content_calendar']}.\n"
                        f"Next renewal date updated from {old_values['old_renewal_date']}\n"
                        f"to {license.next_renewal_date}\n\n"
                    )

                else:  # user is on paid plan, limits will be reset using stripe webhook
                    logger.error(
                        f"User '{user.email}' is on paid plan. Skipping...\n"
                        f"License key: {license.license_key},\n"
                        f"License status: {license.license_status},\n"
                        f"Created on: {license.created_on},\n"
                        f"Next renewal date: {license.next_renewal_date}\n\n"
                    )

            except Exception as e:
                logger.critical(f"Error processing license {license.license_key}: {str(e)}")

        self.stdout.write(
            self.style.SUCCESS(f'Successfully processed {reset_count} licenses')
        )
