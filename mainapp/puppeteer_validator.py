import asyncio
import json
import logging
from typing import Tuple, Optional, List, Dict
from urllib.parse import urlparse, urljoin
import subprocess
import os
import tempfile
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import random
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
import undetected_chromedriver as uc
import ssl
import socket
from urllib3.exceptions import InsecureRequestWarning
import warnings

# Suppress SSL warnings
warnings.filterwarnings('ignore', category=InsecureRequestWarning)

logger = logging.getLogger(__name__)

class EnhancedURLValidator:
    """Enhanced URL validator with multiple validation strategies and better success rate"""
    
    def __init__(self, timeout: int = 20):  # Reduced timeout for faster validation
        self.timeout = timeout
        self.user_agents = [
            # More common user agents that work better
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15'
        ]
        
        self.simple_headers = {
            'User-Agent': '',  # Will be set dynamically
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def get_random_user_agent(self) -> str:
        """Get a random user agent"""
        return random.choice(self.user_agents)
    
    def check_domain_resolution(self, url: str) -> Tuple[bool, str]:
        """Check if domain resolves to IP address"""
        try:
            parsed = urlparse(url)
            domain = parsed.netloc
            
            # Remove port if present
            if ':' in domain:
                domain = domain.split(':')[0]
            
            # Try to resolve domain
            socket.gethostbyname(domain)
            return True, f"Domain resolves: {domain}"
            
        except socket.gaierror:
            return False, f"Domain resolution failed: {parsed.netloc}"
        except Exception as e:
            return False, f"Domain check error: {str(e)}"
    
    def validate_url_lightweight_requests(self, url: str) -> Tuple[bool, str]:
        """Lightweight requests validation with minimal headers"""
        try:
            headers = {
                'User-Agent': self.get_random_user_agent(),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Connection': 'keep-alive',
            }
            
            # Try HEAD request first (faster)
            try:
                response = requests.head(
                    url,
                    timeout=self.timeout,
                    allow_redirects=True,
                    headers=headers,
                    verify=False,  # Skip SSL verification for problematic sites
                )
                
                if response.status_code < 400:
                    return True, f"URL is accessible (HEAD): {response.status_code}"
                elif response.status_code == 405:  # Method not allowed, try GET
                    pass
                else:
                    return False, f"HEAD request failed: {response.status_code}"
            except:
                pass  # Fall back to GET
            
            # Try GET request with range to minimize download
            headers['Range'] = 'bytes=0-1023'  # Only get first 1KB
            
            response = requests.get(
                url,
                timeout=self.timeout,
                allow_redirects=True,
                headers=headers,
                verify=False,
                stream=True  # Don't download full content
            )
            
            if response.status_code < 400 or response.status_code == 206:  # 206 = Partial Content
                return True, f"URL is accessible (GET): {response.status_code}"
            elif response.status_code == 403:
                # 403 might still be a valid site, just blocking bots
                return True, f"URL exists but access restricted: {response.status_code}"
            else:
                return False, f"GET request failed: {response.status_code}"
                
        except requests.exceptions.SSLError:
            # Try with different SSL approach
            try:
                response = requests.get(
                    url,
                    timeout=self.timeout,
                    allow_redirects=True,
                    headers={'User-Agent': self.get_random_user_agent()},
                    verify=False,
                    stream=True
                )
                if response.status_code < 400:
                    return True, f"URL is accessible (SSL bypass): {response.status_code}"
            except:
                pass
            return False, f"SSL error accessing URL: {url}"
        except requests.exceptions.ConnectionError as e:
            return False, f"Connection error: {str(e)}"
        except requests.exceptions.Timeout:
            return False, f"Request timeout: {url}"
        except Exception as e:
            return False, f"Request error: {str(e)}"
    
    def validate_url_with_curl_simple(self, url: str) -> Tuple[bool, str]:
        """Simple curl validation"""
        try:
            # Simple curl with minimal options
            curl_command = [
                'curl', '-s', '-I', '-L',  # Silent, head only, follow redirects
                '--max-time', '15',
                '--user-agent', self.get_random_user_agent(),
                '--insecure',  # Skip SSL verification
                '--connect-timeout', '10',
                url
            ]
            
            result = subprocess.run(
                curl_command,
                capture_output=True,
                text=True,
                timeout=20
            )
            
            if result.returncode == 0:
                # Check for HTTP status
                if 'HTTP/' in result.stdout:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'HTTP/' in line:
                            try:
                                status_code = int(line.split()[1])
                                if status_code < 400:
                                    return True, f"URL is accessible (curl): {status_code}"
                                elif status_code == 403:
                                    return True, f"URL exists but access restricted (curl): {status_code}"
                            except:
                                pass
                
                # If no clear error, assume success
                return True, "URL is accessible (curl - no errors)"
            
            return False, f"Curl failed: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            return False, "Curl timeout"
        except FileNotFoundError:
            return False, "Curl not available"
        except Exception as e:
            return False, f"Curl error: {str(e)}"
    
    def validate_url_with_puppeteer_simple(self, url: str) -> Tuple[bool, str]:
        """Simple Puppeteer validation"""
        try:
            project_root = self.get_project_root()
            
            # Check if puppeteer is available
            if not os.path.exists(os.path.join(project_root, 'node_modules', 'puppeteer')):
                return False, "Puppeteer not available"
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as temp_file:
                temp_file.write(self.create_simple_puppeteer_script(url))
                temp_file_path = temp_file.name
            
            try:
                result = subprocess.run(
                    ['node', temp_file_path],
                    capture_output=True,
                    text=True,
                    timeout=25,
                    cwd=project_root
                )
                
                if result.returncode == 0 and result.stdout.strip():
                    try:
                        output = json.loads(result.stdout.strip())
                        if output.get('success', False):
                            return True, output.get('message', 'URL is accessible (Puppeteer)')
                        else:
                            return False, output.get('message', 'Puppeteer validation failed')
                    except json.JSONDecodeError:
                        return False, "Invalid Puppeteer response"
                
                return False, f"Puppeteer execution failed: {result.stderr}"
                    
            finally:
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
                    
        except Exception as e:
            return False, f"Puppeteer error: {str(e)}"
    
    def create_simple_puppeteer_script(self, url: str) -> str:
        """Create simple Puppeteer script"""
        return f"""
const puppeteer = require('puppeteer');

(async () => {{
    let browser = null;
    try {{
        browser = await puppeteer.launch({{
            headless: 'new',
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--ignore-certificate-errors',
                '--disable-features=VizDisplayCompositor'
            ],
            timeout: 15000
        }});

        const page = await browser.newPage();
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        
        const response = await page.goto('{url}', {{
            waitUntil: 'networkidle2',
            timeout: 15000
        }});
        
        const result = {{
            success: response.status() < 400,
            status_code: response.status(),
            message: `URL is accessible (Puppeteer): ${{response.status()}}`
        }};
        
        console.log(JSON.stringify(result));
        
    }} catch (error) {{
        console.log(JSON.stringify({{
            success: false,
            message: `Puppeteer failed: ${{error.message}}`
        }}));
    }} finally {{
        if (browser) {{
            await browser.close();
        }}
    }}
}})();
"""
    
    def get_project_root(self) -> str:
        """Get the project root directory"""
        try:
            current_dir = os.getcwd()
            while current_dir != os.path.dirname(current_dir):
                if os.path.exists(os.path.join(current_dir, 'package.json')):
                    return current_dir
                current_dir = os.path.dirname(current_dir)
            return os.getcwd()
        except:
            return os.getcwd()
    
    def validate_url_fast(self, url: str) -> Tuple[bool, str]:
        """Fast URL validation with progressive fallback"""
        logger.info(f"Starting fast validation for: {url}")
        
        # Step 1: Domain resolution check (fastest)
        is_valid, message = self.check_domain_resolution(url)
        if not is_valid:
            logger.warning(f"Domain resolution failed: {url}")
            return False, message
        
        # Step 2: Lightweight requests (most reliable)
        logger.info("Trying lightweight requests...")
        is_valid, message = self.validate_url_lightweight_requests(url)
        if is_valid:
            logger.info(f"Lightweight requests successful: {url}")
            return True, message
        
        # Step 3: Curl fallback (good for bot detection)
        logger.info("Trying curl fallback...")
        is_valid, message = self.validate_url_with_curl_simple(url)
        if is_valid:
            logger.info(f"Curl validation successful: {url}")
            return True, message
        
        # Step 4: Puppeteer if available (last resort)
        logger.info("Trying Puppeteer as last resort...")
        is_valid, message = self.validate_url_with_puppeteer_simple(url)
        if is_valid:
            logger.info(f"Puppeteer validation successful: {url}")
            return True, message
        
        logger.warning(f"All validation methods failed for: {url}")
        return False, f"URL validation failed: {url}"


def normalize_url(url: str) -> str:
    """Normalize URL format"""
    url = url.strip()
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    return url


def validate_url_simple(url: str) -> Tuple[bool, str]:
    """Main validation function using fast approach"""
    try:
        url = normalize_url(url)
        
        # Parse URL to ensure it's valid
        parsed = urlparse(url)
        if not all([parsed.scheme, parsed.netloc]):
            return False, f"Invalid URL format: {url}"
        
        # Use fast validation
        validator = EnhancedURLValidator(timeout=20)
        return validator.validate_url_fast(url)
        
    except Exception as e:
        return False, f"URL validation error: {str(e)}"


def validate_url_robust(url: str, use_puppeteer: bool = False) -> Tuple[bool, str]:
    """Robust URL validation with fast approach"""
    return validate_url_simple(url)


def validate_url(url: str) -> bool:
    """Simple boolean validation for CrewAI compatibility"""
    is_valid, _ = validate_url_simple(url)
    return is_valid
    